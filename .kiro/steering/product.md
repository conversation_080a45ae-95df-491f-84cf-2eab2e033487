# Product Overview

## Lost Pet Hong Kong (走失宠物协寻平台)

A comprehensive lost pet finder platform specifically designed for Hong Kong, helping pet owners find their missing pets and enabling the community to report sightings.

### Core Features

- **Pet Registration**: Users can register their pets with photos, descriptions, and characteristics
- **Lost Pet Posts**: Create posts when pets go missing with last seen location and time
- **Sighting Reports**: Community members can report pet sightings with photos and location data
- **Admin Moderation**: Administrative interface for content moderation and platform management
- **Multi-language Support**: Supports English, Simplified Chinese, and Traditional Chinese (Hong Kong)
- **Interactive Maps**: Location-based features using Leaflet maps
- **Email Verification**: Secure user registration with email verification system

### User Types

- **Pet Owners**: Register pets, create lost pet posts, manage their listings
- **Community Members**: Report sightings, help search for lost pets (can be anonymous)
- **Administrators**: Moderate content, manage platform settings, view statistics

### Platform Architecture

- **Client App**: Public-facing Vue.js application for pet owners and community
- **Admin Panel**: Separate Vue.js admin interface for platform management
- **Backend API**: Express.js REST API with MySQL database
- **File Storage**: Local file system for pet and sighting photos