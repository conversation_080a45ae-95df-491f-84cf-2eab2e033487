# Project Structure & Organization

## Root Level Organization

The project follows a multi-application monorepo structure:

```
├── server/          # Backend API server
├── client/          # Public-facing Vue.js app
├── admin/           # Administrative Vue.js app
├── forum_tables.sql # Database schema for forum features
└── sql.sql          # Main database schema
```

## Server Structure (`/server`)

```
server/
├── src/
│   ├── app.js                 # Main Express application
│   ├── controllers/           # Request handlers
│   │   ├── authController.js
│   │   ├── petController.js
│   │   ├── postController.js
│   │   └── sightingController.js
│   ├── models/                # Database models (class-based)
│   │   ├── Pet.js
│   │   ├── Post.js
│   │   ├── User.js
│   │   └── Sighting.js
│   ├── routes/                # Express route definitions
│   └── utils/                 # Utility functions
│       ├── emailService.js
│       ├── jwt.js
│       ├── password.js
│       └── upload.js
├── config/
│   └── db.js                  # Database connection & query utilities
├── scripts/                   # Utility scripts
│   └── createAdmin.js
├── uploads/                   # File storage
│   ├── pets/
│   ├── sightings/
│   └── temp/
└── .env                       # Environment configuration
```

## Client Structure (`/client`)

```
client/
├── src/
│   ├── components/            # Reusable Vue components
│   │   ├── dashboard/         # Dashboard-specific components
│   │   ├── ImageUpload.vue
│   │   ├── MapPicker.vue
│   │   └── PetForm.vue
│   ├── views/                 # Page-level components
│   │   ├── HomeView.vue
│   │   ├── PostsView.vue
│   │   └── DashboardView.vue
│   ├── stores/                # Pinia state management
│   │   └── auth.ts
│   ├── services/              # API service layers
│   │   ├── auth.ts
│   │   ├── pets.ts
│   │   └── posts.ts
│   ├── utils/                 # Utility functions
│   │   ├── api.ts
│   │   ├── helpers.ts
│   │   └── i18n-options.ts
│   ├── locales/               # Internationalization
│   │   ├── en-US.json
│   │   ├── zh-CN.json
│   │   └── zh-HK.json
│   ├── router/                # Vue Router configuration
│   └── assets/                # Static assets
├── scripts/                   # Build/utility scripts
└── tailwind.config.js         # Tailwind CSS configuration
```

## Admin Structure (`/admin`)

```
admin/
├── src/
│   ├── components/            # Admin-specific components
│   ├── views/                 # Admin page components
│   │   ├── Dashboard.vue
│   │   ├── Posts.vue
│   │   └── EmailConfig.vue
│   ├── stores/                # Admin state management
│   ├── api/                   # Admin API services
│   └── utils/                 # Admin utilities
└── (similar build config to client)
```

## Architectural Patterns

### Backend Patterns
- **MVC Architecture**: Controllers handle requests, Models manage data, Routes define endpoints
- **Class-based Models**: Each model is a class with static methods for database operations
- **Middleware Chain**: Authentication, validation, and error handling as Express middleware
- **Connection Pooling**: MySQL connection pool for efficient database access
- **File Organization**: Separate concerns into controllers, models, routes, and utilities

### Frontend Patterns
- **Composition API**: Vue 3 Composition API throughout both applications
- **Service Layer**: Separate API services from components for better organization
- **Store Pattern**: Pinia stores for global state management
- **Component Hierarchy**: Reusable components in `/components`, page-level in `/views`
- **Utility Functions**: Shared utilities in `/utils` directories

### File Naming Conventions
- **Backend**: camelCase for files (e.g., `petController.js`)
- **Frontend**: PascalCase for Vue components (e.g., `PetForm.vue`)
- **Services**: camelCase with descriptive names (e.g., `auth.ts`, `pets.ts`)
- **Database**: snake_case for table/column names
- **Routes**: kebab-case for URL paths

### Import/Export Patterns
- ES modules throughout (`import`/`export`)
- Named exports for utilities and services
- Default exports for Vue components and main modules
- Barrel exports in index files where appropriate

### Configuration Files
- Each application has its own build configuration
- Shared patterns across client and admin (Vite, TypeScript, ESLint)
- Environment-specific configurations in `.env` files