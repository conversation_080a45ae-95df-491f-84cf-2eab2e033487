# Technology Stack & Build System

## Backend (Server)
- **Runtime**: Node.js with ES modules (`"type": "module"`)
- **Framework**: Express.js 4.18+
- **Database**: MySQL 2 with connection pooling
- **Authentication**: JWT (jsonwebtoken) + bcryptjs for password hashing
- **File Upload**: Multer for handling multipart/form-data
- **Validation**: Joi + express-validator
- **Security**: Helmet, CORS, express-rate-limit
- **Email**: Nodemailer for email verification
- **Environment**: dotenv for configuration

## Frontend Applications

### Client App (Public Interface)
- **Framework**: Vue 3 with Composition API + TypeScript
- **Build Tool**: Vite with Rolldown
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Styling**: Tailwind CSS 4 with @tailwindcss/forms
- **UI Components**: @headlessui/vue, @heroicons/vue
- **Maps**: Leaflet + vue-leaflet + leaflet.markercluster
- **Internationalization**: Vue I18n with opencc-js for Chinese conversion
- **HTTP Client**: Axios
- **Utilities**: @vueuse/core, dayjs

### Admin Panel
- **Framework**: Vue 3 with Composition API + TypeScript
- **Build Tool**: Vite with Rolldown
- **State Management**: Pinia
- **UI Library**: Element Plus with @element-plus/icons-vue
- **HTTP Client**: Axios
- **Utilities**: dayjs

## Development Tools
- **Linting**: ESLint + Oxlint with Vue and TypeScript configs
- **Formatting**: Prettier
- **Type Checking**: vue-tsc
- **Package Manager**: Bun (with bun.lock files)
- **Dev Tools**: Vue DevTools, Nodemon for server

## Common Commands

### Server
```bash
# Development
bun run dev          # Start with nodemon
bun run start        # Production start
bun run create-admin # Create admin user
bun run check        # Syntax check

# Code Quality
bun run lint         # ESLint
bun run format       # Prettier
```

### Client/Admin
```bash
# Development
bun run dev          # Start dev server
bun run build        # Production build
bun run preview      # Preview build

# Code Quality
bun run lint         # Run both oxlint and eslint
bun run lint:oxlint  # Fast linting
bun run lint:eslint  # Full ESLint
bun run format       # Prettier
bun run type-check   # TypeScript checking
```

## Environment Configuration
- Server uses `.env` file with database credentials and JWT secrets
- Client proxy configuration in vite.config.ts for `/uploads` route
- CORS configured to allow all origins in development

## File Structure Conventions
- ES modules throughout (import/export syntax)
- TypeScript for frontend applications
- Separate upload directories: `/uploads/pets/`, `/uploads/sightings/`
- Database models use class-based approach with static methods