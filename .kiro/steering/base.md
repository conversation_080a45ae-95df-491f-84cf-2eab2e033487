#一律使用中文和用户沟通
#当存在你无法操作的东西时（例如浏览器操作，密码输入等等）你可以把需求详细说明，请求用户操作，等待用户给你的返回结果。

# 核心命令
#1,禁止你来启动开发服务器，我自己来启动项目（必须遵循），禁止你来演示
#2，每一次更改完代码必须使用各个程序语言的Build方法检查错误
#3, 在所有可行的方案中选择最简单结构，除非我明确指明要求，你就按照最简单的来，不要搞那么复杂。
#4,当实现/修改一个问题有多个方案时，必须先给用户详细说明后，再根据用户的指示执行用户所指定的方案。禁止你直接开始行动，而不向用户说明各个方案。
#5，当你拿到一个需求，你实现的过程中，要把一个需求拆成多个小目标（利用任务机制），分布执行。
#6，不要生成任何带有你的幻觉的代码，当你不确定是使用context7工具查询相关的示例代码。
#7，优先使用curl测试接口，提高速度。
# 开发规范
敏感操作需要二次确认

## 开发环境
- 使用 Bun 作为前端开发工具
- 使用podman作为容器管理工具

##代码质量
-变量和函数命名必须语义化
- 按功能模块划分目录结构
- 工具函数文件名使用 utils
- 常量文件使用 UPPER_SNAKE_CASE

## 代码组织
- 页面内容较多时，采用分步骤创建的方式
- 单次修改和创建代码不超过 400 行，保持代码的可维护性

## ArcGIS 对象处理
- 禁止使用 ref 存储 ArcGIS 对象
- 原因：会导致代理对象访问只读属性错误
  ```typescript
  // ❌ 错误示例
  const map = ref(null)
  
  // ✅ 正确示例
  let map = null
  ```
- 使用普通变量存储需要显示的 ArcGIS 对象内容

在存储vue响应式变量时一定要使用json反序列话消除代理对象

# 工具使用
## 优先使用tavily进行搜索

#测试
##你可以使用各种方法测试你的修改是否通过，但是通过后要删除你生成的测试文件