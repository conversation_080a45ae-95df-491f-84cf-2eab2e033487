{"name": "client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.5.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "opencc-js": "^1.0.5", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-i18n": "9", "vue-leaflet": "^0.1.0", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.2", "@types/leaflet": "^1.9.20", "@types/leaflet.markercluster": "^1.5.5", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "postcss": "^8.5.6", "prettier": "3.5.3", "serve": "^14.2.3", "tailwindcss": "^4.1.11", "typescript": "~5.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}