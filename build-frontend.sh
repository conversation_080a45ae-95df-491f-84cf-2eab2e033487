#!/bin/bash

# 构建前端应用脚本

echo "🏗️  构建前端应用..."

# 构建用户端
if [ -d "client" ]; then
    echo "📦 构建用户端..."
    cd client
    bun install
    bun run build
    cd ..
    echo "✅ 用户端构建完成"
else
    echo "❌ client 目录不存在"
fi

# 构建管理员端
if [ -d "admin" ]; then
    echo "📦 构建管理员端..."
    cd admin
    bun install
    bun run build
    cd ..
    echo "✅ 管理员端构建完成"
else
    echo "❌ admin 目录不存在"
fi

echo "🎉 前端构建完成！"
echo ""
echo "现在可以运行 ./start-server.sh 启动所有服务"