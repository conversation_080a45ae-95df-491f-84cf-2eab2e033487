import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { testConnection } from '../config/db.js';
// 导入路由
import authRoutes from './routes/auth.js';
import petRoutes from './routes/pets.js';
import postRoutes from './routes/posts.js';
import sightingRoutes from './routes/sightings.js';
import emailConfigRoutes from './routes/emailConfig.js';
import emailVerificationRoutes from './routes/emailVerification.js';
// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet());

// CORS配置 - 允许所有源访问
const corsOptions = {
  origin: '*',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// 解析JSON和URL编码的请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 添加CORS头
app.use('/uploads', (_req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
}, express.static('../uploads'));

// 健康检查端点
app.get('/health', (_req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});



// API路由
app.get('/api', (_req, res) => {
  res.json({
    message: '走失宠物协寻平台 API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      pets: '/api/pets',
      posts: '/api/posts',
      sightings: '/api/sightings',
      admin: '/api/admin'
    }
  });
});

// 注册路由
app.use('/api/auth', authRoutes);
app.use('/api/pets', petRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/sightings', sightingRoutes);
app.use('/api/admin/email-config', emailConfigRoutes);
app.use('/api/email-verification', emailVerificationRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.originalUrl
  });
});

// 全局错误处理中间件
app.use((err, _req, res, _next) => {
  console.error('服务器错误:', err);

  // 开发环境显示详细错误信息
  if (process.env.NODE_ENV === 'development') {
    res.status(err.status || 500).json({
      error: err.message,
      stack: err.stack
    });
  } else {
    // 生产环境只显示通用错误信息
    res.status(err.status || 500).json({
      error: '服务器内部错误'
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    app.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`);
      console.log(`📱 客户端地址: ${process.env.CLIENT_URL || 'http://localhost:5173'}`);
      console.log(`🔧 管理后台地址: ${process.env.ADMIN_URL || 'http://localhost:5174'}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

startServer();

export default app;
