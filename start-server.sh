#!/bin/bash

# Lost Pet Hong Kong 完整服务启动脚本

echo "🚀 启动 Lost Pet Hong Kong 完整服务..."

# 检查是否安装了PM2
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2 未安装，正在安装..."
    npm install -g pm2
fi

# 检查是否安装了serve（用于前端静态文件服务）
if ! command -v serve &> /dev/null; then
    echo "📦 安装 serve..."
    npm install -g serve
fi

# 创建日志目录
mkdir -p logs

# 检查环境文件
if [ ! -f "server/.env" ]; then
    echo "⚠️  警告: server/.env 文件不存在，请确保已配置环境变量"
fi

# 检查前端构建文件
if [ ! -d "client/dist" ]; then
    echo "⚠️  警告: client/dist 目录不存在，请先构建用户端前端"
fi

if [ ! -d "admin/dist" ]; then
    echo "⚠️  警告: admin/dist 目录不存在，请先构建管理员端前端"
fi

# 停止现有进程（如果存在）
echo "🛑 停止现有进程..."
pm2 stop lost-pet-server lost-pet-client lost-pet-admin 2>/dev/null || true
pm2 delete lost-pet-server lost-pet-client lost-pet-admin 2>/dev/null || true

# 启动所有服务
echo "▶️  启动所有服务..."
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup

echo "✅ 所有服务启动完成！"
echo ""
echo "🌐 服务访问地址:"
echo "   - 后端API: http://localhost:3000"
echo "   - 用户端: http://localhost:4000"
echo "   - 管理员端: http://localhost:5000"
echo ""
echo "📊 查看状态: pm2 status"
echo "📝 查看日志: pm2 logs"
echo "🔄 重启服务: pm2 restart all"
echo "🛑 停止服务: pm2 stop all"